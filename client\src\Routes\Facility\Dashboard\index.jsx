import { useEffect, useState } from "react";
import { getFacilityMeAPI, updateFacilityMeAPI } from "../../../api";
import { userDataStore, roles } from "../../../data";
import toast from "react-hot-toast";

export default function FacilityDashboard() {
  const userData = userDataStore.useStore({ setter: false });
  const [facility, setFacility] = useState(null);
  const [edit, setEdit] = useState(false);
  const [form, setForm] = useState({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    // Access control: only facilityManager
    if (userData?.data?.role !== roles.facilityManager) {
      toast.error("Access denied: Facility managers only");
      return;
    }
    const fetchFacility = async () => {
      setLoading(true);
      const [err, data] = await getFacilityMeAPI(userData.token);
      setLoading(false);
      if (err) {
        toast.error("Failed to load facility");
        return;
      }
      setFacility(data);
      setForm({ ...data });
    };
    fetchFacility();
  }, [userData?.data?.role]);

  const handleChange = (field) => (e) => {
    setForm((prev) => ({ ...prev, [field]: e.target.value }));
  };

  const handleSave = async () => {
    setSaving(true);
    const payload = {
      name: form.name,
      type: form.type,
      address: form.address,
      phone: form.phone,
      img: form.img,
      is_deleted: form.is_deleted,
    };
    const [err, updated] = await updateFacilityMeAPI(userData.token, payload);
    setSaving(false);
    if (err) {
      toast.error("Failed to save");
      return;
    }
    setFacility(updated);
    setForm(updated);
    setEdit(false);
    toast.success("Saved");
  };

  if (userData?.data?.role !== roles.facilityManager) {
    return (
      <div className="p-6">
        <h1 className="text-xl font-bold">Access denied</h1>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h1 className="mb-6 text-2xl font-bold text-gray-800">Facility Dashboard</h1>

      {loading ? (
        <div>Loading...</div>
      ) : !facility ? (
        <div className="text-red-600">No facility found</div>
      ) : (
        <div className="bg-white rounded shadow p-4 max-w-2xl">
          {!edit ? (
            <div className="space-y-2">
              <div>
                <span className="font-semibold">Name:</span> {facility.name}
              </div>
              <div>
                <span className="font-semibold">Type:</span> {facility.type}
              </div>
              <div>
                <span className="font-semibold">Address:</span> {facility.address}
              </div>
              <div>
                <span className="font-semibold">Phone:</span> {facility.phone || "-"}
              </div>
              <div>
                <span className="font-semibold">Image URL:</span> {facility.img || "-"}
              </div>
              <div>
                <span className="font-semibold">Created At:</span> {facility.createdAt || "-"}
              </div>
              <div>
                <span className="font-semibold">Updated At:</span> {facility.updatedAt || "-"}
              </div>
              <div>
                <span className="font-semibold">Deleted:</span> {facility.is_deleted ? "Yes" : "No"}
              </div>
              <div className="flex gap-2 pt-2">
                <button
                  className="px-4 py-2 bg-blue-600 text-white rounded"
                  onClick={() => setEdit(true)}
                >
                  Edit
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium">Name</label>
                <input
                  className="border rounded px-3 py-2 w-full"
                  value={form.name || ""}
                  onChange={handleChange("name")}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium">Type</label>
                <select
                  className="border rounded px-3 py-2 w-full"
                  value={form.type || "clinic"}
                  onChange={handleChange("type")}
                >
                  <option value="hospital">Hospital</option>
                  <option value="clinic">Clinic</option>
                  <option value="cabinet">Cabinet</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium">Address</label>
                <input
                  className="border rounded px-3 py-2 w-full"
                  value={form.address || ""}
                  onChange={handleChange("address")}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium">Phone</label>
                <input
                  className="border rounded px-3 py-2 w-full"
                  value={form.phone || ""}
                  onChange={handleChange("phone")}
                />
              </div>
              <div>
                <label className="block text-sm font-medium">Image URL</label>
                <input
                  className="border rounded px-3 py-2 w-full"
                  value={form.img || ""}
                  onChange={handleChange("img")}
                />
              </div>
              <div className="flex gap-2 pt-2">
                <button
                  className="px-4 py-2 bg-gray-200 rounded"
                  onClick={() => {
                    setEdit(false);
                    setForm(facility);
                  }}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 bg-blue-600 text-white rounded"
                  onClick={handleSave}
                  disabled={saving}
                >
                  {saving ? "Saving..." : "Save"}
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

